use std::sync::atomic::{AtomicUsize, Ordering};

// 全局变量
static TICKS: AtomicUsize = AtomicUsize::new(0);
static mut STEER_ORG: usize = 0;
static mut TRACKING_ENABLED: bool = true;
static mut LAST_TARGET_POS: [f32; 3] = [0.0, 0.0, 0.0];

// 简化的追踪配置
struct TrackingConfig {
    max_range: f32,
    prediction_time: f32,
    angle_threshold: f32,
    update_frequency: u32,
}

static TRACKING_CONFIG: TrackingConfig = TrackingConfig {
    max_range: 2500.0,
    prediction_time: 0.25,
    angle_threshold: 45.0,
    update_frequency: 3,
};

// 简化的追踪逻辑
unsafe fn should_track_target() -> bool {
    if !TRACKING_ENABLED {
        return false;
    }

    // 每N个tick检查一次
    let current_tick = TICKS.load(Ordering::Relaxed);
    if current_tick % TRACKING_CONFIG.update_frequency as usize != 0 {
        return false;
    }

    true
}

// 计算简单的追踪坐标
unsafe fn get_tracking_coordinates() -> Option<[f32; 3]> {
    // 这里可以实现简单的追踪逻辑
    // 例如：向鼠标方向移动，或者使用预设的坐标

    // 示例：返回一个稍微偏移的坐标来实现"抖动"效果
    let tick = TICKS.load(Ordering::Relaxed);
    if tick % 10 == 0 {
        // 每10个tick返回一个稍微不同的坐标
        Some([
            LAST_TARGET_POS[0] + (tick as f32 * 0.1) % 100.0,
            LAST_TARGET_POS[1],
            LAST_TARGET_POS[2] + (tick as f32 * 0.1) % 100.0,
        ])
    } else {
        None
    }
}

// 主要的 hook 函数（简化版）
#[no_mangle]
pub extern "fastcall" fn on_steer(a1: usize, a2: usize, slot: u8, coords: &mut [f32; 3], finish: u8) {
    unsafe {
        // 增加tick计数
        TICKS.fetch_add(1, Ordering::Relaxed);

        // 只对R技能（slot 3）生效，并且启用了追踪
        if slot == 3 && should_track_target() {
            // 获取追踪坐标
            if let Some(target_coords) = get_tracking_coordinates() {
                // 保存当前目标位置
                LAST_TARGET_POS = *coords;

                // 设置新的追踪坐标
                coords[0] = target_coords[0];
                coords[1] = target_coords[1];
                coords[2] = target_coords[2];
            }
        }

        // 调用原函数
        if STEER_ORG != 0 {
            let org_fn: extern "fastcall" fn(usize, usize, u8, &mut [f32; 3], u8) =
                std::mem::transmute(STEER_ORG);
            org_fn(a1, a2, slot, coords, finish);
        }
    }
}

// 初始化 hook
#[no_mangle]
pub extern "C" fn init_sion_hook(original_function: usize) {
    unsafe {
        STEER_ORG = original_function;
        TRACKING_ENABLED = true;
    }
}

// 启用/禁用追踪
#[no_mangle]
pub extern "C" fn set_tracking_enabled(enabled: bool) {
    unsafe {
        TRACKING_ENABLED = enabled;
    }
}

// 设置追踪参数
#[no_mangle]
pub extern "C" fn set_tracking_params(max_range: f32, prediction_time: f32, angle_threshold: f32) {
    // 注意：由于TRACKING_CONFIG是静态的，这里需要使用其他方式来更新参数
    // 可以考虑使用静态可变变量或其他方法
}

// 获取当前tick计数
#[no_mangle]
pub extern "C" fn get_tick_count() -> usize {
    TICKS.load(Ordering::Relaxed)
}

// 重置tick计数
#[no_mangle]
pub extern "C" fn reset_tick_count() {
    TICKS.store(0, Ordering::Relaxed);
}

// 设置目标位置（从Lua脚本调用）
#[no_mangle]
pub extern "C" fn set_target_position(x: f32, y: f32, z: f32) {
    unsafe {
        LAST_TARGET_POS = [x, y, z];
    }
}
