use std::sync::atomic::{AtomicUsize, Ordering, AtomicBool};

// 全局变量
static TICKS: AtomicUsize = AtomicUsize::new(0);
static mut STEER_ORG: usize = 0;
static CONTROL_ENABLED: AtomicBool = AtomicBool::new(true);
static mut MOUSE_POS: [f32; 3] = [0.0, 0.0, 0.0];
static mut PLAYER_POS: [f32; 3] = [0.0, 0.0, 0.0];
static mut TARGET_ENEMY_POS: [f32; 3] = [0.0, 0.0, 0.0];
static mut CONTROL_MODE: u8 = 0; // 0=自动追踪, 1=鼠标控制, 2=手动控制

// 控制参数
struct ControlConfig {
    update_frequency: u32,
    prediction_time: f32,
    max_tracking_range: f32,
    turn_sensitivity: f32,
}

static CONTROL_CONFIG: ControlConfig = ControlConfig {
    update_frequency: 2,  // 每2个tick更新一次，更频繁的控制
    prediction_time: 0.2,
    max_tracking_range: 2500.0,
    turn_sensitivity: 1.0,
};

// 检查是否应该控制方向
unsafe fn should_control_direction() -> bool {
    if !CONTROL_ENABLED.load(Ordering::Relaxed) {
        return false;
    }

    // 每N个tick更新一次
    let current_tick = TICKS.load(Ordering::Relaxed);
    current_tick % CONTROL_CONFIG.update_frequency as usize == 0
}

// 计算新的方向坐标
unsafe fn calculate_new_direction(current_coords: &[f32; 3]) -> Option<[f32; 3]> {
    match CONTROL_MODE {
        1 => calculate_mouse_direction(current_coords),      // 鼠标控制
        2 => calculate_manual_direction(current_coords),     // 手动控制
        _ => calculate_auto_tracking(current_coords),        // 自动追踪
    }
}

// 鼠标方向控制
unsafe fn calculate_mouse_direction(current_coords: &[f32; 3]) -> Option<[f32; 3]> {
    // 计算从玩家位置到鼠标位置的方向
    let dx = MOUSE_POS[0] - PLAYER_POS[0];
    let dz = MOUSE_POS[2] - PLAYER_POS[2];

    // 归一化方向向量
    let length = (dx * dx + dz * dz).sqrt();
    if length > 0.1 {
        let normalized_x = dx / length;
        let normalized_z = dz / length;

        // 计算新的目标位置（在鼠标方向上延伸）
        let distance = 2000.0; // 延伸距离
        Some([
            PLAYER_POS[0] + normalized_x * distance,
            current_coords[1], // 保持Y坐标不变
            PLAYER_POS[2] + normalized_z * distance,
        ])
    } else {
        None
    }
}

// 手动方向控制（可以通过外部函数设置方向）
unsafe fn calculate_manual_direction(current_coords: &[f32; 3]) -> Option<[f32; 3]> {
    // 这里可以根据按键输入来调整方向
    // 例如：左转、右转等
    Some(*current_coords) // 暂时返回当前坐标
}

// 自动追踪最近的敌人
unsafe fn calculate_auto_tracking(current_coords: &[f32; 3]) -> Option<[f32; 3]> {
    // 如果有目标敌人位置，追踪它
    if TARGET_ENEMY_POS[0] != 0.0 || TARGET_ENEMY_POS[2] != 0.0 {
        // 预测敌人位置
        let prediction_offset = CONTROL_CONFIG.prediction_time * 350.0; // 假设敌人移动速度

        Some([
            TARGET_ENEMY_POS[0] + prediction_offset,
            current_coords[1],
            TARGET_ENEMY_POS[2] + prediction_offset,
        ])
    } else {
        None
    }
}

// 主要的 hook 函数 - 塞恩大招方向控制
#[no_mangle]
pub extern "fastcall" fn on_steer(a1: usize, a2: usize, slot: u8, coords: &mut [f32; 3], finish: u8) {
    unsafe {
        // 增加tick计数
        TICKS.fetch_add(1, Ordering::Relaxed);

        // 只对塞恩的R技能（slot 3）生效
        if slot == 3 && should_control_direction() {
            // 计算新的方向坐标
            if let Some(new_coords) = calculate_new_direction(coords) {
                // 应用新的坐标
                coords[0] = new_coords[0];
                coords[1] = new_coords[1];
                coords[2] = new_coords[2];
            }
        }

        // 调用原函数
        if STEER_ORG != 0 {
            let org_fn: extern "fastcall" fn(usize, usize, u8, &mut [f32; 3], u8) =
                std::mem::transmute(STEER_ORG);
            org_fn(a1, a2, slot, coords, finish);
        }
    }
}

// 初始化 hook
#[no_mangle]
pub extern "C" fn init_sion_hook(original_function: usize) {
    unsafe {
        STEER_ORG = original_function;
        CONTROL_ENABLED.store(true, Ordering::Relaxed);
        CONTROL_MODE = 1; // 默认鼠标控制模式
    }
}

// 启用/禁用控制
#[no_mangle]
pub extern "C" fn set_control_enabled(enabled: bool) {
    CONTROL_ENABLED.store(enabled, Ordering::Relaxed);
}

// 设置控制模式 (0=自动追踪, 1=鼠标控制, 2=手动控制)
#[no_mangle]
pub extern "C" fn set_control_mode(mode: u8) {
    unsafe {
        CONTROL_MODE = mode;
    }
}

// 更新鼠标位置
#[no_mangle]
pub extern "C" fn update_mouse_position(x: f32, y: f32, z: f32) {
    unsafe {
        MOUSE_POS = [x, y, z];
    }
}

// 更新玩家位置
#[no_mangle]
pub extern "C" fn update_player_position(x: f32, y: f32, z: f32) {
    unsafe {
        PLAYER_POS = [x, y, z];
    }
}

// 设置目标敌人位置
#[no_mangle]
pub extern "C" fn set_target_enemy_position(x: f32, y: f32, z: f32) {
    unsafe {
        TARGET_ENEMY_POS = [x, y, z];
    }
}

// 手动设置方向（用于按键控制）
#[no_mangle]
pub extern "C" fn manual_steer_direction(angle_offset: f32) {
    unsafe {
        if CONTROL_MODE == 2 {
            // 根据角度偏移计算新的方向
            // 这里可以实现左转/右转逻辑
        }
    }
}

// 获取当前tick计数
#[no_mangle]
pub extern "C" fn get_tick_count() -> usize {
    TICKS.load(Ordering::Relaxed)
}

// 重置tick计数
#[no_mangle]
pub extern "C" fn reset_tick_count() {
    TICKS.store(0, Ordering::Relaxed);
}

// 获取当前控制状态
#[no_mangle]
pub extern "C" fn get_control_status() -> u8 {
    unsafe {
        if CONTROL_ENABLED.load(Ordering::Relaxed) {
            CONTROL_MODE
        } else {
            255 // 表示禁用
        }
    }
}
