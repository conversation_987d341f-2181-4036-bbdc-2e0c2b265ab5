use std::sync::atomic::{AtomicUsize, Ordering};
use std::ffi::c_void;

// 全局变量
static TICKS: AtomicUsize = AtomicUsize::new(0);
static mut STEER_ORG: usize = 0;
static mut SION_R_ACTIVE: bool = false;
static mut CURRENT_TARGET: Option<GameUnit> = None;

// 游戏单位结构体
#[repr(C)]
struct GameUnit {
    pos: [f32; 3],
    health: f32,
    max_health: f32,
    team: u32,
    is_visible: bool,
    is_targetable: bool,
    char_name: [u8; 64],
}

// 玩家信息结构体
#[repr(C)]
struct Player {
    pos: [f32; 3],
    direction: [f32; 3],
    has_sion_r_buff: bool,
    char_name: [u8; 64],
}

// 外部函数声明（需要从游戏内存中获取）
extern "C" {
    fn get_player() -> *const Player;
    fn get_enemies_in_range(pos: [f32; 3], range: f32) -> *const GameUnit;
    fn get_enemy_count() -> u32;
    fn predict_position(unit: *const GameUnit, time: f32) -> [f32; 3];
    fn calculate_angle_between(pos1: [f32; 3], pos2: [f32; 3], pos3: [f32; 3]) -> f32;
    fn is_sion() -> bool;
}

// 检查是否为塞恩并且有R buff
unsafe fn is_sion_with_r_buff() -> bool {
    if !is_sion() {
        return false;
    }
    
    let player = get_player();
    if player.is_null() {
        return false;
    }
    
    (*player).has_sion_r_buff
}

// 获取最佳追踪目标
unsafe fn get_best_tracking_target() -> Option<[f32; 3]> {
    let player = get_player();
    if player.is_null() {
        return None;
    }
    
    let player_pos = (*player).pos;
    let player_dir = (*player).direction;
    
    let mut best_target: Option<*const GameUnit> = None;
    let mut best_score = 0.0f32;
    
    // 遍历所有敌人
    let enemy_count = get_enemy_count();
    for i in 0..enemy_count {
        let enemy = get_enemies_in_range(player_pos, 2500.0);
        if enemy.is_null() {
            continue;
        }
        
        let enemy_ref = &*enemy.offset(i as isize);
        
        // 检查敌人是否有效
        if !enemy_ref.is_visible || !enemy_ref.is_targetable || enemy_ref.team == (*player).team {
            continue;
        }
        
        // 预测敌人位置
        let predicted_pos = predict_position(enemy_ref, 0.3);
        
        // 计算距离分数
        let distance = ((predicted_pos[0] - player_pos[0]).powi(2) + 
                       (predicted_pos[1] - player_pos[1]).powi(2) + 
                       (predicted_pos[2] - player_pos[2]).powi(2)).sqrt();
        let distance_score = (2500.0 - distance).max(0.0) / 2500.0;
        
        // 计算角度分数
        let angle = calculate_angle_between(player_pos, predicted_pos, 
                                          [player_pos[0] + player_dir[0], 
                                           player_pos[1] + player_dir[1], 
                                           player_pos[2] + player_dir[2]]);
        let angle_deg = angle.to_degrees().abs();
        let angle_score = (90.0 - angle_deg).max(0.0) / 90.0;
        
        // 计算血量分数
        let health_score = 1.0 - (enemy_ref.health / enemy_ref.max_health);
        
        // 综合分数
        let mut total_score = distance_score * 0.4 + angle_score * 0.4 + health_score * 0.2;
        
        // 如果目标在前进方向上，额外加分
        if angle_deg < 30.0 {
            total_score *= 1.3;
        }
        
        // 如果是低血量目标，额外加分
        if enemy_ref.health / enemy_ref.max_health < 0.3 {
            total_score *= 1.2;
        }
        
        if total_score > best_score && total_score > 0.3 {
            best_target = Some(enemy_ref);
            best_score = total_score;
        }
    }
    
    // 如果找到了好目标，返回预测位置
    if let Some(target) = best_target {
        let predicted_pos = predict_position(target, 0.25);
        Some(predicted_pos)
    } else {
        None
    }
}

// 主要的 hook 函数
#[no_mangle]
pub extern "fastcall" fn on_steer(a1: usize, a2: usize, slot: u8, coords: &mut [f32; 3], finish: u8) {
    unsafe {
        // 只对塞恩的R技能（slot 3）生效
        if slot == 3 && is_sion_with_r_buff() {
            // 获取最佳追踪目标
            if let Some(target_pos) = get_best_tracking_target() {
                // 设置追踪坐标
                coords[0] = target_pos[0];
                coords[1] = target_pos[1];
                coords[2] = target_pos[2];
                
                // 调用原函数
                let org_fn: extern "fastcall" fn(usize, usize, u8, &mut [f32; 3], u8) =
                    std::mem::transmute(STEER_ORG);
                org_fn(a1, a2, slot, coords, finish);
                return;
            }
        }
        
        // 对于其他情况，正常调用原函数
        let org_fn: extern "fastcall" fn(usize, usize, u8, &mut [f32; 3], u8) =
            std::mem::transmute(STEER_ORG);
        org_fn(a1, a2, slot, coords, finish);
    }
}

// 初始化 hook
#[no_mangle]
pub extern "C" fn init_sion_hook(original_function: usize) {
    unsafe {
        STEER_ORG = original_function;
    }
}

// 设置塞恩R状态
#[no_mangle]
pub extern "C" fn set_sion_r_active(active: bool) {
    unsafe {
        SION_R_ACTIVE = active;
    }
}

// 获取当前tick计数
#[no_mangle]
pub extern "C" fn get_tick_count() -> usize {
    TICKS.load(Ordering::Relaxed)
}

// 重置tick计数
#[no_mangle]
pub extern "C" fn reset_tick_count() {
    TICKS.store(0, Ordering::Relaxed);
}
