# 塞恩大招自由控制系统

## 功能概述

这个系统为塞恩的大招提供了完全的方向控制，让你可以随意改变大招的方向来追踪敌人或避开障碍物。

## 文件说明

### 1. Champions/Sion.lua (主脚本)
- 包含了塞恩的所有技能逻辑
- 集成了智能追踪和直接控制功能
- 提供了菜单配置选项

### 2. sion_r_hook.rs (Rust Hook)
- 底层hook函数，直接修改游戏中的方向坐标
- 提供三种控制模式：自动追踪、鼠标控制、手动控制
- 实时更新玩家和目标位置

## 使用方法

### 基础使用
1. 确保 Sion.lua 脚本已加载
2. 编译并注入 sion_r_hook.rs
3. 按 L 键启用大招追踪
4. 使用大招时会自动追踪最佳目标

### 控制模式

#### 1. 自动追踪模式 (默认)
- 自动寻找最佳目标
- 优先攻击低血量敌人
- 考虑距离、角度和英雄优先级

#### 2. 鼠标控制模式
- 大招方向跟随鼠标位置
- 实时调整方向
- 适合精确控制

#### 3. 手动控制模式
- 使用按键控制方向
- Q键左转，E键右转
- 可自定义转向速度

### 菜单设置

在游戏中打开塞恩脚本菜单，找到"大招"部分：

#### 基础设置
- **追踪使用** (L键): 启用/禁用大招追踪
- **使用模式**: 选择新版本、旧版本或测试模式
- **暂时关闭** (A键): 临时禁用追踪
- **原地不动** (S键): 停止移动
- **追踪目标** (H键): 追踪特定目标

#### 智能追踪设置
- **启用智能追踪**: 开启AI追踪功能
- **追踪模式**: 激进/保守/平衡
- **智能追踪范围**: 设置追踪距离 (1500-3000)
- **最小分数阈值**: 目标选择的最低要求

#### 目标优先级
- **优先ADC**: 优先攻击ADC英雄
- **优先低血量**: 优先攻击残血敌人
- **优先脆皮**: 优先攻击脆皮英雄

## 按键说明

| 按键 | 功能 |
|------|------|
| L | 启用/禁用大招追踪 |
| A | 暂时关闭追踪 |
| S | 原地停止 |
| H | 追踪指定目标 |
| Q | 手动左转 (手动模式) |
| E | 手动右转 (手动模式) |

## 工作原理

### Lua脚本层面
1. **目标选择**: 分析所有敌方英雄，计算优先级分数
2. **预测位置**: 预测敌人0.25秒后的位置
3. **方向计算**: 计算最佳攻击角度和距离
4. **执行控制**: 使用 player:attack() 或 player:attackmove()

### Rust Hook层面
1. **拦截坐标**: Hook游戏的方向设置函数
2. **实时修改**: 每2-3个tick修改一次方向坐标
3. **多模式支持**: 根据设置选择不同的控制算法
4. **平滑控制**: 避免方向突变，提供流畅的控制体验

## 优化建议

### 性能优化
- 降低更新频率可以减少CPU占用
- 调整预测时间来平衡准确性和性能

### 准确性优化
- 根据不同英雄调整预测参数
- 考虑敌人的移动模式和技能

### 安全性优化
- 避免过于明显的机械化操作
- 添加随机性来模拟人类操作

## 故障排除

### 常见问题

1. **大招不追踪目标**
   - 检查L键是否启用
   - 确认目标在追踪范围内
   - 检查最小分数阈值设置

2. **方向控制不生效**
   - 确认Rust hook已正确注入
   - 检查控制模式设置
   - 验证玩家和鼠标位置更新

3. **追踪目标不准确**
   - 调整预测时间参数
   - 修改目标优先级设置
   - 检查英雄优先级配置

### 调试模式
启用调试模式可以看到：
- 当前追踪的目标
- 目标选择的分数计算
- 预测位置和实际位置的差异

## 注意事项

1. **合规使用**: 确保在允许使用脚本的环境中使用
2. **性能影响**: 过高的更新频率可能影响游戏性能
3. **检测风险**: 底层hook可能被反作弊系统检测
4. **版本兼容**: 游戏更新可能需要调整hook地址

## 更新日志

### v1.0
- 基础的大招追踪功能
- 简单的目标选择算法

### v2.0
- 添加Rust hook支持
- 多种控制模式
- 智能目标优先级

### v2.1 (当前版本)
- 优化了方向控制算法
- 添加了直接控制功能
- 改进了用户界面
- 增强了调试功能

## 技术支持

如果遇到问题，请检查：
1. 脚本是否正确加载
2. Hook是否成功注入
3. 菜单设置是否正确
4. 游戏版本是否兼容

建议在测试环境中先验证功能，确认无误后再在正式环境中使用。
