-- 塞恩大招追踪配置文件
local SionConfig = {}

-- 追踪参数配置
SionConfig.tracking = {
    -- 基础参数
    max_range = 2500,           -- 最大追踪距离
    prediction_time = 0.25,     -- 预测时间
    min_score_threshold = 0.3,  -- 最小分数阈值
    
    -- 分数权重
    distance_weight = 0.4,      -- 距离权重
    angle_weight = 0.4,         -- 角度权重
    health_weight = 0.2,        -- 血量权重
    
    -- 角度参数
    max_angle = 90,             -- 最大追踪角度
    preferred_angle = 30,       -- 优选角度（小于此角度会加分）
    
    -- 血量参数
    low_health_threshold = 0.3, -- 低血量阈值
    low_health_bonus = 1.2,     -- 低血量加成
    
    -- 优选角度加成
    preferred_angle_bonus = 1.3,
    
    -- 更新频率
    update_frequency = 5,       -- 每N个tick更新一次
}

-- 目标优先级配置
SionConfig.priority = {
    -- 英雄优先级（数值越高优先级越高）
    champions = {
        -- ADC
        ["Jinx"] = 10,
        ["<PERSON><PERSON>"] = 10,
        ["Caitlyn"] = 9,
        ["<PERSON>"] = 9,
        ["<PERSON><PERSON>"] = 9,
        ["<PERSON>"] = 8,
        ["<PERSON><PERSON>"] = 8,
        ["Twitch"] = 10,
        ["Kog'Maw"] = 9,
        ["Varus"] = 8,
        
        -- 中单法师
        ["Syndra"] = 9,
        ["Orianna"] = 8,
        ["Azir"] = 9,
        ["Cassiopeia"] = 9,
        ["Ryze"] = 8,
        ["<PERSON>"] = 8,
        ["Vel'Koz"] = 9,
        ["Xerath"] = 9,
        
        -- 刺客
        ["Zed"] = 8,
        ["Talon"] = 8,
        ["Katarina"] = 8,
        ["Akali"] = 7,
        ["Fizz"] = 7,
        ["LeBlanc"] = 8,
        
        -- 辅助
        ["Soraka"] = 9,
        ["Janna"] = 8,
        ["Lulu"] = 8,
        ["Nami"] = 7,
        ["Sona"] = 8,
        
        -- 坦克（较低优先级）
        ["Malphite"] = 3,
        ["Rammus"] = 3,
        ["Amumu"] = 4,
        ["Sejuani"] = 4,
        ["Nautilus"] = 4,
        ["Leona"] = 4,
        
        -- 默认优先级
        default = 5
    }
}

-- 智能追踪模式配置
SionConfig.smart_tracking = {
    enabled = true,
    
    -- 追踪模式
    modes = {
        aggressive = {
            max_range = 2500,
            min_score = 0.2,
            prefer_low_health = true,
            health_bonus = 1.5
        },
        
        conservative = {
            max_range = 2000,
            min_score = 0.4,
            prefer_low_health = false,
            health_bonus = 1.1
        },
        
        balanced = {
            max_range = 2200,
            min_score = 0.3,
            prefer_low_health = true,
            health_bonus = 1.2
        }
    },
    
    current_mode = "balanced"
}

-- 防撞墙配置
SionConfig.wall_detection = {
    enabled = true,
    check_distance = 700,       -- 检查距离
    check_interval = 50,        -- 检查间隔
    safety_margin = 100         -- 安全边距
}

-- 小兵穿透配置
SionConfig.minion_penetration = {
    enabled = true,
    stop_on_minions = true,     -- 遇到小兵时停止
    minion_check_range = 800,   -- 小兵检查范围
    auto_stop_delay = 0.5       -- 自动停止延迟
}

-- 调试配置
SionConfig.debug = {
    enabled = false,
    show_target_info = true,
    show_score_calculation = false,
    show_prediction_lines = false,
    log_tracking_decisions = true
}

-- 获取英雄优先级
function SionConfig:GetChampionPriority(champion_name)
    return self.priority.champions[champion_name] or self.priority.champions.default
end

-- 获取当前追踪模式配置
function SionConfig:GetCurrentModeConfig()
    local mode = self.smart_tracking.current_mode
    return self.smart_tracking.modes[mode] or self.smart_tracking.modes.balanced
end

-- 设置追踪模式
function SionConfig:SetTrackingMode(mode)
    if self.smart_tracking.modes[mode] then
        self.smart_tracking.current_mode = mode
        return true
    end
    return false
end

-- 计算目标分数
function SionConfig:CalculateTargetScore(distance, angle_deg, health_percent, champion_name)
    local config = self:GetCurrentModeConfig()
    local tracking = self.tracking
    
    -- 基础分数计算
    local distance_score = math.max(0, (config.max_range - distance) / config.max_range)
    local angle_score = math.max(0, (tracking.max_angle - angle_deg) / tracking.max_angle)
    local health_score = 1.0 - health_percent
    
    -- 英雄优先级加成
    local champion_priority = self:GetChampionPriority(champion_name)
    local priority_bonus = champion_priority / 10.0
    
    -- 计算基础分数
    local base_score = distance_score * tracking.distance_weight + 
                      angle_score * tracking.angle_weight + 
                      health_score * tracking.health_weight
    
    -- 应用优先级加成
    local final_score = base_score * priority_bonus
    
    -- 角度加成
    if angle_deg < tracking.preferred_angle then
        final_score = final_score * tracking.preferred_angle_bonus
    end
    
    -- 低血量加成
    if config.prefer_low_health and health_percent < tracking.low_health_threshold then
        final_score = final_score * config.health_bonus
    end
    
    return final_score
end

-- 验证配置
function SionConfig:ValidateConfig()
    local errors = {}
    
    -- 检查基础参数
    if self.tracking.max_range <= 0 then
        table.insert(errors, "max_range must be positive")
    end
    
    if self.tracking.prediction_time < 0 then
        table.insert(errors, "prediction_time cannot be negative")
    end
    
    -- 检查权重总和
    local weight_sum = self.tracking.distance_weight + 
                      self.tracking.angle_weight + 
                      self.tracking.health_weight
    if math.abs(weight_sum - 1.0) > 0.01 then
        table.insert(errors, "Weight sum should be 1.0, got " .. weight_sum)
    end
    
    return #errors == 0, errors
end

-- 重置为默认配置
function SionConfig:ResetToDefaults()
    -- 这里可以重置所有配置到默认值
    self.smart_tracking.current_mode = "balanced"
end

return SionConfig
