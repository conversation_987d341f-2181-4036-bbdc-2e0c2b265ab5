@echo off
chcp 65001 >nul
echo 正在编译塞恩大招控制Hook...

REM 检查是否安装了Rust
where rustc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Rust编译器，请先安装Rust
    echo 下载地址: https://rustup.rs/
    pause
    exit /b 1
)

REM 清理旧文件
if exist "sion_hook" (
    echo 清理旧文件...
    rmdir /s /q sion_hook
)

REM 创建新的Cargo项目
echo 创建Cargo项目...
cargo new sion_hook --lib

REM 创建正确的Cargo.toml
echo 配置Cargo.toml...
(
echo [package]
echo name = "sion_hook"
echo version = "0.1.0"
echo edition = "2021"
echo.
echo [lib]
echo crate-type = ["cdylib"]
echo.
echo [dependencies]
) > sion_hook\Cargo.toml

REM 复制源代码
echo 复制源代码...
copy /y sion_r_hook.rs sion_hook\src\lib.rs

REM 编译项目
echo 开始编译...
cd sion_hook
cargo build --release

if %errorlevel% equ 0 (
    echo.
    echo ✅ 编译成功！
    echo 📁 输出文件: target\release\sion_hook.dll
    echo 📏 文件大小:
    dir target\release\sion_hook.dll | find "sion_hook.dll"
    echo.
    echo 📋 使用说明:
    echo 1. 将 sion_hook.dll 注入到游戏进程
    echo 2. 调用 init_sion_hook^(原函数地址^) 初始化
    echo 3. 使用 set_control_enabled^(true^) 启用控制
    echo 4. 根据需要调用其他配置函数
    echo.
    echo 🎯 Hook函数列表:
    echo - init_sion_hook^(addr^)
    echo - set_control_enabled^(bool^)
    echo - set_control_mode^(0-2^)
    echo - update_mouse_position^(x,y,z^)
    echo - update_player_position^(x,y,z^)
    echo - set_target_enemy_position^(x,y,z^)
    echo.
) else (
    echo ❌ 编译失败，请检查错误信息
    echo.
    echo 常见问题:
    echo 1. 确保Rust工具链已正确安装
    echo 2. 检查源代码语法错误
    echo 3. 确保有足够的磁盘空间
)

cd ..
echo.
echo 按任意键退出...
pause >nul
