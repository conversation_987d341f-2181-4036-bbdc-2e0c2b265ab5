@echo off
echo 正在编译塞恩大招控制Hook...

REM 检查是否安装了Rust
where rustc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Rust编译器，请先安装Rust
    echo 下载地址: https://rustup.rs/
    pause
    exit /b 1
)

REM 创建Cargo项目结构
if not exist "sion_hook" (
    echo 创建项目目录...
    mkdir sion_hook
    mkdir sion_hook\src
)

REM 创建Cargo.toml
echo 创建Cargo.toml...
(
echo [package]
echo name = "sion_hook"
echo version = "0.1.0"
echo edition = "2021"
echo.
echo [lib]
echo crate-type = ["cdylib"]
echo.
echo [dependencies]
) > sion_hook\Cargo.toml

REM 复制源代码
echo 复制源代码...
copy sion_r_hook.rs sion_hook\src\lib.rs

REM 编译项目
echo 开始编译...
cd sion_hook
cargo build --release

if %errorlevel% equ 0 (
    echo.
    echo 编译成功！
    echo 输出文件: target\release\sion_hook.dll
    echo.
    echo 使用说明:
    echo 1. 将 sion_hook.dll 注入到游戏进程
    echo 2. 调用 init_sion_hook(原函数地址) 初始化
    echo 3. 使用 set_control_enabled(true) 启用控制
    echo 4. 根据需要调用其他配置函数
    echo.
) else (
    echo 编译失败，请检查错误信息
)

cd ..
pause
