local Sion = {}
local ID = header.id
local AsVerification = module.load(ID, "Libdata/lib_Verification")
local AsUtility = module.load(ID, "Libdata/Utility")
local AsEvadeSpell = module.load(ID, "Libdata/Spell")
local AsTargetSelectorMenu = module.load(ID, "TS/TargetSelector")
module.load(header.id, 'TS/TargetSelector').addToMenu() --目标选择器
local evade = module.seek("evade") or module.seek("evade_old")
local preds = module.internal("pred")
local orb = module.internal("orb")
local TS = module.internal("TS")
local polygon, polygons, clipper, clipper_offset, clipper_enum =  unpack(module.internal('clipper'))

-- 加载塞恩配置
local SionConfig = dofile("sion_config.lua")
local lastAA = 0
local lasttarget = nil
local lasttime = 0

local QLevelDageme = {30, 50, 70 , 90, 110}	
local WLevelDageme = {75, 125, 175, 225, 275}	
local ELevelDageme = {22, 34, 46, 58, 70}	
local RLevelDageme =  {300, 475, 650}	
local iswxhl = AsUtility.iswxhl()
local QMANA,WMANA,EMANA,RMANA = 0,0,0,0
local drawcombo=(graphics.width<=1920 and graphics.height<=1080) or false
local chinese = AsUtility.ischinese()
local SionCastDirection = nil
local lastq = 0
local lastAATarget = nil
local Q =  {range = 0}
local W =  {range = 0}
local E =  {range = 0}
local R =  {range = 0}
local not_name = {PlantSatchel = true,PlantVision = true,PlantHealth = true,}
Q =  {slot = player:spellSlot(0); delay = 0.5; range = 800; width = 40; speed = math.huge; boundingRadiusMod = 0; collision = { hero = true, minion = true };}	
W =  {slot = player:spellSlot(1); delay = 0.25; range = 0; width = 160; speed = 1800; boundingRadiusMod = 0; collision = { hero = true, minion = true };}	
E =  {slot = player:spellSlot(2); delay = 0.25; range = 1200;width = 80; speed = 1800; boundingRadiusMod = 1; collision = { hero = true, minion = true };}	
R =  {slot = player:spellSlot(3); delay = 0.25; range = 2500;width = 120; radius = 120; boundingRadiusMod = 0; collision = { hero = true, minion = false}; speed = 2000;}	
--local FlashSlot = assert(loadstring(AsVerification.cloud['FlashSlot']))()

if AsTargetSelectorMenu then
	AsTargetSelectorMenu.SetQ(Q.range)
	AsTargetSelectorMenu.SetE(E.range)
	AsTargetSelectorMenu.SetR(R.range)
end
if AsVerification  and AsVerification.IsSuccess then
	if chinese then
		Sion.menu = menu("[Brian] - Sion",AsVerification.name .." 亡灵战神");
		Sion.menu:menu("q", "猛击")
		--Sion.menu.q:header("combo1","连招设置")
		Sion.menu.q:boolean("combo","连招使用", true)
		
		
		Sion.menu:menu("w", "熔炉")
		--Sion.menu.w:header("combo1","连招设置")
		Sion.menu.w:boolean("combo","连招使用", true)
		
		Sion.menu.w:header("combo1","护盾设置")
		Sion.menu.w:boolean("auto","自动使用", true)
		Sion.menu.w:boolean("aa", "格挡AA", true)
		Sion.menu.w:slider("aahp", "低于HP", 40, 1, 100, 5)
		
		
		
		Sion.menu:menu("e", "怒吼")
		--Sion.menu.e:header("combo1","连招设置")
		Sion.menu.e:boolean("combo","连招使用", true)
		Sion.menu.e:boolean("ext","穿兵使用", true)
		
		Sion.menu.e:header("combo1","杂项")
		Sion.menu.e:keybind('auto', '自动使用',nil, 'U')
		Sion.menu.e:menu("automenu", "自动使用列表")
		
		for i=0, objManager.enemies_n - 1 do
		  local enemy = objManager.enemies[i]
		  Sion.menu.e.automenu:header('as', enemy.charName)
		  Sion.menu.e.automenu:boolean(enemy.charName, '自动E: ' .. enemy.charName, true)
		end
		--[[--]]
		Sion.menu:menu("r", "大招")
		Sion.menu.r:keybind('to', '追踪使用',nil, 'L')
		Sion.menu.r:dropdown("mode", "使用模式", 1, {"新版本", "旧版本","测试"})

		Sion.menu.r:keybind('zt', '暂时关闭',"A", nil)
		Sion.menu.r:keybind('stop', '原地不动',"S", nil)
		Sion.menu.r:keybind('to_target', '追踪目标',"H", nil)
		Sion.menu.r:slider("to_target_range", "追踪的目标距离鼠标<范围", 2000, 100, 3000, 10)

		-- 智能追踪设置
		Sion.menu.r:header("smart_header", "智能追踪设置")
		Sion.menu.r:boolean("smart_tracking", "启用智能追踪", true)
		Sion.menu.r:dropdown("tracking_mode", "追踪模式", 2, {"激进", "保守", "平衡"})
		Sion.menu.r:slider("smart_range", "智能追踪范围", 2200, 1500, 3000, 50)
		Sion.menu.r:slider("min_score", "最小分数阈值", 30, 10, 80, 5)

		-- 目标优先级设置
		Sion.menu.r:header("priority_header", "目标优先级")
		Sion.menu.r:boolean("prioritize_adc", "优先ADC", true)
		Sion.menu.r:boolean("prioritize_low_hp", "优先低血量", true)
		Sion.menu.r:boolean("prioritize_squishy", "优先脆皮", true)
		
		
		--旧版本
		Sion.menu.r:boolean("target","优先追踪目标", false)
		Sion.menu.r:boolean("close","穿透小兵自动停止追踪", true)
		Sion.menu.r:boolean("fz","防止撞墙", true)
		
		
		--Sion.menu:header("qh", "清线设置")
		Sion.menu:menu("farm", "清线")
		--Sion.menu.farm:header("jgHeader", "[清线/打野设置]")
		Sion.menu.farm:keybind("qx", "清线打野使用", nil, "G")
		Sion.menu.farm:boolean('qxjs', '清线检测周围是否有地方英雄',true)
		Sion.menu.farm:header("jgHeader", "[Q 技能设置]")
		Sion.menu.farm:boolean("quse","清线使用", true)
		Sion.menu.farm:slider('qmana', '清线蓝量管理',50, 0, 100, 1)
		Sion.menu.farm:slider('qqxsl', '命中最小数',3, 0, 10, 1)
		
		Sion.menu.farm:header("jgHeader", "[E 技能设置]")
		Sion.menu.farm:boolean("euse","清线使用", true)
		Sion.menu.farm:slider('emana', '清线蓝量管理',50, 0, 100, 1)
		Sion.menu.farm:slider('eqxsl', '命中最小数',3, 0, 10, 1)
		
		
		--Sion.menu:header("kil1l","按键设置")
		Sion.menu:menu("keys", "按键")
		--Sion.menu.keys:header("kil1l","按键设置")
		Sion.menu.keys:keybind("combokey", "连招", "Space", nil)
		Sion.menu.keys:keybind("harasskey", "骚扰", "C", nil)
		Sion.menu.keys:keybind("clearkey", "清线/清野", "V", nil)
		Sion.menu.keys:keybind("lastkey", "最后一击", "X", nil)
		Sion.menu.keys:keybind("fleekey", "逃跑", "Z", nil)
		
		--Sion.menu:header("kil1l","其他设置")
		--Sion.menu:boolean("dev","开发者模式", false)
	else
		Sion.menu = menu("[Brian]  Sion",  AsVerification.name .." Sion");
		Sion.menu:menu("q", "Q Spell Settings")
		Sion.menu.q:header("combo1","Combo Settings")
		Sion.menu.q:boolean("combo","Combo Use", true)
		
		Sion.menu:menu("w", "W Spell Settings")
		Sion.menu.w:header("combo1","Combo Settings")
		Sion.menu.w:boolean("combo","Combo Use", true)
		
		Sion.menu.w:header("combo1","Shield Settings")
		Sion.menu.w:boolean("auto","Auto Use", true)
		Sion.menu.w:boolean("aa", "Block AA", true)
		Sion.menu.w:slider("aahp", "Block HP", 40, 1, 100, 5)
		
		
		
		Sion.menu:menu("e", "E Spell Settings")
		Sion.menu.e:header("combo1","Combo Settings")
		Sion.menu.e:boolean("combo","Combo Use", true)
		Sion.menu.e:boolean("ext","Ext Use", true)
		
		Sion.menu.e:header("combo1","Misc Settings")
		Sion.menu.e:keybind('auto', 'Auto Use',nil, 'U')
		Sion.menu.e:menu("automenu", "Auto Menu")
		
		for i=0, objManager.enemies_n - 1 do
		  local enemy = objManager.enemies[i]
		  Sion.menu.e.automenu:header('as', enemy.charName)
		  Sion.menu.e.automenu:boolean(enemy.charName, 'Auto E: ' .. enemy.charName, true)
		end
		--[[--]]
		Sion.menu:menu("r", "R Spell Settings")
		Sion.menu.r:keybind('to', 'BUG',nil, 'L')
		Sion.menu.r:dropdown("mode", "Mode", 1, {"NEW", "OLD"})
		Sion.menu.r:keybind('zt', 'Temporarily closed',"A", nil)
		Sion.menu.r:keybind('stop', 'stand still',"S", nil)
		Sion.menu.r:keybind('to_target', 'Track target',"H", nil)
		Sion.menu.r:slider("to_target_range", "Track target < mousePos range", 1500, 100, 3000, 10)
		
		
		
		
		--旧版本
		Sion.menu.r:boolean("target","Prioritize tracking Target", false)
		Sion.menu.r:boolean("close","Penetrating creeps automatically stop tracking", true)
		Sion.menu.r:boolean("fz","Prevent hitting the wall", true)
		
		
		Sion.menu:header("qh", "Farm Settings")
		Sion.menu:menu("farm", "Farm Settings")
		Sion.menu.farm:header("jgHeader", "[Farm Settings]")
		Sion.menu.farm:keybind("qx", "Clear Use", nil, "G")
		Sion.menu.farm:boolean('qxjs', 'Check Hero',true)
		Sion.menu.farm:header("jgHeader", "[Q Spell Settings]")
		Sion.menu.farm:boolean("quse","Clear Use", true)
		Sion.menu.farm:slider('qmana', 'Mana',50, 0, 100, 1)
		Sion.menu.farm:slider('qqxsl', 'Hit',3, 0, 10, 1)
		
		Sion.menu.farm:header("jgHeader", "[E Spell Settings]")
		Sion.menu.farm:boolean("euse","Clear Use", true)
		Sion.menu.farm:slider('emana', 'Mana',50, 0, 100, 1)
		Sion.menu.farm:slider('eqxsl', 'Hit',3, 0, 10, 1)
		
		
		Sion.menu:header("kil1l","Key Settings")
		Sion.menu:menu("keys", "Key Settings")
		Sion.menu.keys:header("kil1l","Key Settings")
		Sion.menu.keys:keybind("combokey", "Combo", "Space", nil)
		Sion.menu.keys:keybind("harasskey", "Haras", "C", nil)
		Sion.menu.keys:keybind("clearkey", "Clear", "V", nil)
		Sion.menu.keys:keybind("lastkey", "Last", "X", nil)
		Sion.menu.keys:keybind("fleekey", "Flee", "Z", nil)
		
		--Sion.menu:header("kil1l","Debug Settings")
		--Sion.menu:boolean("dev","Dev Mode", false)
	end
	Sion.menu.q:slider("line", "角度", 230, 1, 300, 5)
	if Sion.menu.r.mode then
		if Sion.menu.r.mode:get() == 1 then
			Sion.menu.r.target:set("visible", false)
			Sion.menu.r.close:set("visible", false)
			Sion.menu.r.fz:set("visible", false)
		else
			Sion.menu.r.to_target_range:set("visible", false)
			Sion.menu.r.to_target:set("visible", false)
		end
		Sion.menu.r.mode:set('callback', function(v)
			if v ~= Sion.menu.r.mode:get() then
				if v == 1 then
					Sion.menu.r.target:set("visible", true)
					Sion.menu.r.close:set("visible", true)
					Sion.menu.r.fz:set("visible", true)
					Sion.menu.r.to_target_range:set("visible", false)
					Sion.menu.r.to_target:set("visible", false)
				elseif v == 2 then
					Sion.menu.r.target:set("visible", false)
					Sion.menu.r.close:set("visible", false)
					Sion.menu.r.fz:set("visible", false)
					Sion.menu.r.to_target_range:set("visible", true)
					Sion.menu.r.to_target:set("visible", true)
				end
			end
		end)
	end
	
	
	local f = function(res, obj, dist)
	  if obj then
		 return true
	  end
	 
	end
	local sadebug = function(msg)
		--if Sion.menu.dev:get() then
			--chat.clear()
			--chat.add("[SA Debug]: " .. msg)
			--chat.add(os.clock())
			--chat.print()
			--print("[SA Debug]: " .. msg)
		--end
	end

	local GetTarget = function(range)
		AsTargetSelectorMenu.SetQ(Q.range)
	AsTargetSelectorMenu.SetE(E.range)
	AsTargetSelectorMenu.SetR(R.range)
	  if AsTargetSelectorMenu.last == 0 then
			return TS.get_result(f).obj
	  else	
		 if AsTargetSelectorMenu.target then
			return AsTargetSelectorMenu.target
		 end
	  end
	end
	
	
	local QAD = {0.45,0.525,0.6 ,0.675 ,0.75}
	local function GetQDageme(unit)
		local qlevel = player:spellSlot(0).level
		local QDmg = QLevelDageme[qlevel] or 0;
		
		local totalQAdDmg = QDmg + (AsUtility.GetTotalAD() * QAD[qlevel] );
		local total =  AsUtility.CalculatePhysicalDamage(unit,totalQAdDmg)
		return total
	end
	local function GetWDageme(unit)
		local wlevel = player:spellSlot(1).level
		local Dmg = WLevelDageme[wlevel] or 0;
		
		local totalQAdDmg = Dmg + (AsUtility.GetTotalAP() *.7);
		local total =  AsUtility.CalculateMagicDamage(unit,totalQAdDmg)
		return total
	end
	local function GetEDageme(unit)
		local Elevel = player:spellSlot(2).level
		local Dmg = ELevelDageme[Elevel] or 0;
		
		local totalQAdDmg = Dmg + (AsUtility.GetTotalAP() *.3);
		local total =  AsUtility.CalculateMagicDamage(unit,totalQAdDmg)
		return total
	end
	
	local function GetRDageme(unit)
		local Rlevel = player:spellSlot(3).level
		local Dmg = RLevelDageme[Rlevel] or 0;
		
		local totalQAdDmg = Dmg + (AsUtility.GetTotalAP() *.3);
		local total =  AsUtility.CalculateMagicDamage(unit,totalQAdDmg)
		return total
	end
	
	
	local trace_filter = function(input,segment, target)
		if preds.trace.linear.hardlock(input, segment, target) then
			return true
		end
		if preds.trace.linear.hardlockmove(input, segment, target) then
			return true
		end
		if iswxhl then return true end
		local d1 = segment.startPos:dist(segment.endPos)
		local d2 = input.range
		local d3 = segment.startPos:dist(target.path.serverPos2D)
		if d1 < 500 then
			return true
		end
		if preds.trace.newpath(target, 0.033, 0.500) then
			return true
		end
	end
	
	local trace_filterc = function(input,segment, target)
		if preds.trace.circular.hardlock(input, segment, target) then
			return true
		end
		if preds.trace.circular.hardlockmove(input, segment, target) then
			return true
		end
		local d1 = segment.startPos:dist(segment.endPos)
		local d2 = input.range
		local d3 = segment.startPos:dist(target.path.serverPos2D)
		if d1 > (d2 + 65) then
			return false
		end
		if d2 + 65 < d3 then
			return false
		end
		if d1 < 700 then
			return true
		end
		if preds.trace.newpath(target, 0.033, 0.500) then
			return true
		end
	end
	
	local GetQrange = function()
		local range = 300
		if player.buff['sionq'] then
			local lasttime = game.time - player.buff['sionq'].startTime + network.latency + 0.08
			range = math.min(Q.range, 300 + math.floor(lasttime*10) * 30)
		end
		return range
	end

	local checkRange = function(pos,range,tb)
		local t = false
		for i=1, #tb, 1 do
			local t = tb[i]
			if t:distSqr(pos) < range^2  then
				t = true
				return true
			end
		end
		return t
	end
	
	local checkCastSpell = function(target)
		if target.buff['garenq'] and target.pos:dist(player.pos)<225 then
			return true
		end
		return false
	end
	
	local FoundEtarget=function(unit)  -- AOE
	  if not unit or not AsUtility.isTarget(unit) then return end
	  local Etarget={}
	  local Ecount=false
	  local Temp = {}
	  Q.collision = {hero = true,minion = false}
	  for i = 0 , objManager.enemies_n - 1 do 
		local obj = objManager.enemies[i]
		if obj and AsUtility.isTarget(obj) and obj~=unit and obj.team ~= TEAM_ALLY and  obj.pos:dist(player.path.serverPos) <= 675 then 
		  local seg = preds.linear.get_prediction(Q, obj)
		  if seg and seg.endPos and seg.startPos:dist(seg.endPos) <= 675 then
			local collision=preds.collision.get_prediction(Q, seg)
			if collision then
			  if #collision>1 then
				Ecount=true
			  end
			  table.insert(Etarget,{unit=obj,count=#collision,type=1})
			end
		  end
		end
	  end
	  
	  
	  if Ecount then
		table.sort(Etarget,function(a,b)
			if a.count and b.count then
			  return a.count>b.count
			end
		end)
	  else
		table.sort(Etarget,function(a,b)
			if a.unit.pos and b.unit.pos then
				return a.unit.pos:dist(player.pos)<b.unit.pos:dist(player.pos)
			end
		end)
	  end
	  if Etarget[1] then
		return Etarget[1]
	  end
	end
	
	
	local CheckQCast = function()
		local rt = true
		local rt_target = nil
		local enemy = AsUtility.GetEnemyHeroes()
		for i, enemies in ipairs(enemy) do
			if enemies and enemies.isVisible and AsUtility.isTarget(enemies) and  enemies.pos:dist(player.pos) <= 850 then
				if checkCastSpell(enemies) then
					return true
				end
				local end_pos = player.pos + player.direction * 800
				local tpred = AsUtility.GetPredictedPos2(enemies,0.2)
				local line = mathf.dist_line_vector(tpred,vec2(end_pos.x,end_pos.z),player.pos2D)
				if line < Sion.menu.q.line:get() and vec3(tpred.x, enemies.y,tpred.y):dist(player.pos) < 800 and end_pos:dist(player.pos) >  end_pos:dist(vec3(tpred.x,enemies.y,tpred.y)) then -- 倆边
					return false 
				elseif AsUtility.isw() and W.slot.name == "SionW" then
					local line1 = mathf.dist_line_vector(enemies.pos2D,vec2(end_pos.x,end_pos.z),player.pos2D)
					local pointSegment, pointLine, isOnSegment = AsUtility.VectorPointProjectionOnLineSegment3(player.pos,end_pos,enemies)
					if not isOnSegment then --不在Q范围内
						rt = true;
						local tpred2 = AsUtility.GetPredictedPos2(enemies,1.1)
						if vec3(tpred2.x,enemies.y,tpred2.y):dist(player.pos) < 789 then
							rt_target = enemies
						end
					elseif line > Sion.menu.q.line:get() then --不在Q范围内
						rt = true;
						local tpred2 = AsUtility.GetPredictedPos2(enemies,1.1)
						if vec3(tpred2.x,enemies.y,tpred2.y):dist(player.pos) < 789 then
							rt_target = enemies
						end
					end
				end
				
				
			end
		end
		return rt,rt_target
	end
	
	
	--UdyrTurtleAttack
	local logicQ = function()
		--preds.linear.get_prediction(input, tar, src)
		--preds.circular.get_prediction(input, tar, src)
		--pred.collision.get_prediction(input, pred_result, ign_obj)
		--and  not AsUtility.YasuoWall_check(player.path.serverPos,Target.pos) 
		if player.buff['sionq'] and Sion.menu.keys.combokey:get() and Sion.menu.q.combo:get()   then
			local check_q_cast,check_q_cast_target = CheckQCast()
			if check_q_cast then
				local cast = true
				--[[
				if check_q_cast_target and AsUtility.isw() and W.slot.name == "SionW" then
					local FoundEtarget = FoundEtarget(check_q_cast_target)
					if FoundEtarget and AsUtility.isTarget(FoundEtarget.unit)  then
						local range = 780
						local seg = preds.linear.get_prediction(Q, check_q_cast_target)
						local seg2= preds.linear.get_prediction(Q, FoundEtarget.unit)
						if seg and seg2 and seg2.endPos and seg2.startPos:dist(seg2.endPos)<780   then
							player:castSpell("self",1)
							player:castSpell("pos",0,vec3(seg2.endPos.x,mousePos.y,seg2.endPos.y))
							sadebug("Combo BUG Aoe Q")
							Target = FoundEtarget.unit
							AsTargetSelectorMenu.SetTarget(FoundEtarget.unit)
							orb.core.set_server_pause()
							Sion.close_q = os.clock() + 0.25
							return true
						end
					else
						local seg = preds.linear.get_prediction(Q, check_q_cast_target)
						if seg and seg.startPos:dist(seg.endPos) <= 780 and trace_filter(Q,seg,check_q_cast_target) then
							player:castSpell("self",1)
							player:castSpell("pos",0,vec3(seg.endPos.x,mousePos.y,seg.endPos.y))
							sadebug("Combo BUG Q1")
							orb.core.set_server_pause()
							Sion.close_q = os.clock() + 0.25
							return true
						end
					end
				end
				--]]
				if not Sion.close_q or Sion.close_q < os.clock() then
					player:castSpell("release", 0,player.pos)
					sadebug("release q")
					return true	
				end
			end
		end
		if player.buff['sionq'] then
			if AsUtility.CountEnemiesInRange1(player.pos,800) == 0 and Sion.menu.keys.combokey:get() then
				player:castSpell("release",0,player.pos)
				orb.core.set_server_pause()
				SionCastDirection = nil
				return
			end
			if evade then
				local spell = AsEvadeSpell.GetEvadeSpell(player.pos2D,0.2,player)
				if spell and spell.slot and spell.char_name then
					print("qcccccccccc - " .. spell.name )
					if spell.data and spell.data.spell_type and (spell.data.spell_type == "Target" or spell.data.spell_type == "target") then
						
						if spell.name:find("udyrturtleattack") or spell.name:find("udyrtigerattack") or spell.name:find("leonashieldofdaybreakattack")  then
							player:castSpell("pos", 0,player.pos)
							sadebug("spell.data_target : " .. spell.name)
							return true
						end
					end
					if AsEvadeSpell.YKSpell[spell.char_name]  then
						local slot = AsEvadeSpell.get_evade_name_slot[spell.name] or spell.slot
						local iscc = AsEvadeSpell.YKSpell[spell.char_name][slot]
						if iscc then
							player:castSpell("release",0,player.pos)
							orb.core.set_server_pause()
							sadebug("spell.data_missile : " .. spell.name)
							SionCastDirection = nil
							return true
						end
					end
				end
			end
			
			
		end
		if Sion.menu.keys.combokey:get() and Sion.menu.q.combo:get() and player.mana > QMANA then
			local target = AsTargetSelectorMenu.gettarget(800,1)
			if target and AsUtility.isTarget(target) and target.pos:dist(player.path.serverPos) <= 800   then
				if not player.buff['sionq'] and game.time > lastAA and orb.core.can_action() then
					local FoundEtarget = FoundEtarget(target)
				
					if FoundEtarget and AsUtility.isTarget(FoundEtarget.unit)  then
						local range = 780
						local seg = preds.linear.get_prediction(Q, target)
						local seg2= preds.linear.get_prediction(Q, FoundEtarget.unit)
						if seg and seg2 and seg2.endPos and seg2.startPos:dist(seg2.endPos)<675  and trace_filter(Q,seg,target) then
							player:castSpell("pos",0,vec3(seg2.endPos.x,mousePos.y,seg2.endPos.y))
							sadebug("Combo Aoe Q")
							Target = FoundEtarget.unit
							AsTargetSelectorMenu.SetTarget(FoundEtarget.unit)
							orb.core.set_server_pause()
							return true
						end
					else
						local seg = preds.linear.get_prediction(Q, target)
						if seg and seg.startPos:dist(seg.endPos) <= 675 and trace_filter(Q,seg,target) then
							player:castSpell("pos",0,vec3(seg.endPos.x,mousePos.y,seg.endPos.y))
							sadebug("Combo Q1")
							orb.core.set_server_pause()
							return true
						end
					end
				end
			end
		end
	end
	
	local logicW = function()
		--preds.linear.get_prediction(input, tar, src)
		--preds.circular.get_prediction(input, tar, src)
		--pred.collision.get_prediction(input, pred_result, ign_obj)
		if player.buff['sionq'] then return end
		
		if Sion.menu.w.auto:get() and player.mana > WMANA and W.slot.name == "SionW" then
			if AsUtility.isnotmove(player) and  #AsUtility.CountEnemiesInRange(player.pos,600) > 0 then
				player:castSpell("self", 1)
				return true
			end
			if evade then
				local spell = AsEvadeSpell.GetEvadeSpell(player.pos2D,0.12,player)
				if spell and spell.slot and spell.char_name then
					if not spell.name:find("basicattack") then
						player:castSpell("self", 1)
						sadebug("spell.data_target : " .. spell.name)
						return true
					end
					
				end
			end
			
		end
	end
	
	
	local GetEPos = function(target)
		local Position = preds.linear.get_prediction(E, target) 
		if not Position then return end
		local valid = {}
		if Sion.menu.e.ext:get() then
			local minions = objManager.minions
			for i = 0, minions.size[TEAM_ENEMY] - 1 do
				local minion = minions[TEAM_ENEMY][i]
				if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead   and player.path.serverPos:distSqr(minion.path.serverPos) <= ( 800 *  800) and minion.pos:dist(target.pos) < player.pos:dist(target.pos) and minion.maxHealth > 10  then
					table.insert(valid, minion)
				end
			end
			if #valid == 0 then
				for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
					local minion = minions[TEAM_NEUTRAL][i]
					if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead  and player.path.serverPos:distSqr(minion.path.serverPos) <= ( 800 *  800) and minion.pos:dist(target.pos) < player.pos:dist(target.pos) and minion.maxHealth > 10  then
						table.insert(valid, minion)
					end
				end
			end
		end
		
		
		if #valid > 0 then
			if vec3(Position.endPos.x,mousePos.y,Position.endPos.y):dist(player.pos) < E.range then
				for i, minion_a in ipairs(valid) do
					local tpred = minion_a.pos2D --minion_a.path.active and preds.core.project(vec2(player.path.serverPos.x, player.path.serverPos.z), minion_a.path, 0.25, 1400, minion_a.moveSpeed) or vec2(minion_a.path.serverPos.x, minion_a.path.serverPos.z)
					local distance = math.min( player.attackRange, math.max(800, tpred:dist(player.pos2D)))
					local direction = ( minion_a.pos - player.pos):norm()
					for j=0, E.range-distance, 60 do
						local extendedPos = player.pos + direction * (j+distance)
						if vec3(Position.endPos.x,mousePos.y,Position.endPos.y):dist(extendedPos) <= 64 then
							return minion_a.pos
						end
					end
				end
			end
		end
	end
	
	
	
	local logicE = function()
		if Target and AsUtility.isTarget(Target) and player.mana >EMANA and game.time > lastAA and orb.core.can_action()  then
			if Sion.menu.keys.combokey:get() and Sion.menu.e.combo:get() and Target.pos:dist(player.path.serverPos) <= 750 then  --combo
				local seg = preds.linear.get_prediction(E, Target)
				if seg and seg.startPos:dist(seg.endPos) <= 750 and trace_filter(E,seg,Target) then
					player:castSpell("pos",2,vec3(seg.endPos.x,mousePos.y,seg.endPos.y))
					sadebug("Combo E")
					return true
				end
			elseif Sion.menu.keys.combokey:get() and Sion.menu.e.combo:get() and Target.pos:dist(player.path.serverPos) <= E.range then  --combo
				local epos = GetEPos(Target)
				if epos then
					player:castSpell("pos",2,epos)
					sadebug("E Ext Combo")
					return true
				end
			end
		end
		if Sion.menu.e.auto:get() and  not player.isRecalling and game.time > lastAA and orb.core.can_action() then
			for i = 0, objManager.enemies_n - 1 do
				local source = objManager.enemies[i]
				if source and not source.isDead and source.isTargetable and AsUtility.isTarget(source) and source.pos:dist(player) < E.range then
					if Sion.menu.e.auto:get()  and Sion.menu.e.automenu[source.charName] and Sion.menu.e.automenu[source.charName]:get()  then
						local epos = GetEPos(source)
						if epos then
							player:castSpell("pos",2,epos)
							sadebug("E Ext Auto" .. source.charName)
							return true
						end
					
					end
				
				end
			end
		end
	end
	
	-- 获取最佳R技能追踪目标（优化版本）
	local function GetRTarget()
		local r = nil
		local angle = 30
		local best_priority = 0

		-- 优先追踪当前目标选择器的目标
		if AsTargetSelectorMenu.focus and AsUtility.isTarget(AsTargetSelectorMenu.focus) then
			local focus_angle = mathf.angle_between(mousePos2D, player.pos2D, AsTargetSelectorMenu.focus.pos2D)
			local focus_angle_deg = math.abs(math.deg(focus_angle))
			if focus_angle_deg < 45 then
				return AsTargetSelectorMenu.focus
			end
		end

		local preds_pos = preds.core.get_pos_after_time(player, 0.25)

		-- 首先寻找敌方英雄
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and AsUtility.isTarget(enemy) and enemy.pos:dist(player.pos) <= 2000 then
				local predicted_pos = AsUtility.GetPredictedPos2(enemy, 0.25)
				local pos = player.path.serverPos2D + (mousePos2D - player.path.serverPos2D):norm()*10000
				local a = mathf.angle_between(pos, player.pos2D, predicted_pos)
				local enemy_angle = math.abs(math.deg(a))

				-- 计算优先级：距离越近，角度越小，优先级越高
				local distance_factor = math.max(0, 2000 - enemy.pos:dist(player.pos)) / 2000
				local angle_factor = math.max(0, 45 - enemy_angle) / 45
				local priority = distance_factor * 0.6 + angle_factor * 0.4

				-- 如果敌人血量较低，提高优先级
				if enemy.health / enemy.maxHealth < 0.3 then
					priority = priority * 1.5
				end

				if enemy_angle < angle and priority > best_priority then
					r = enemy
					angle = enemy_angle
					best_priority = priority
				end
			end
		end

		-- 如果没有找到合适的英雄，寻找其他目标
		if not r then
			objManager.loop(function(obj)
				if obj and obj.isTargetable and (obj.isVisible or obj.type == TYPE_TURRET) then
					local preds_pos_obj = obj.pos2D
					local range = 800
					if obj.type == 2 then
						range = 300
					end
					if not not_name[obj.name] and preds_pos_obj:dist(preds_pos) <= range and obj.team ~= TEAM_ALLY then
						local pos = player.path.serverPos2D + (mousePos2D - player.path.serverPos2D):norm()*10000
						local a = mathf.angle_between(pos, player.pos2D, obj.pos2D)
						local minionAngle = math.abs(math.deg(a))
						if minionAngle < angle and player.pos:dist(obj.pos) > mousePos:dist(obj.pos) then
							r = obj
							angle = minionAngle
						end
					end
				end
			end)
		end

		return r
	end

	-- 智能大招追踪函数（使用配置）
	local function SmartRTracking()
		if not player.buff['sionr'] or not SionConfig.smart_tracking.enabled then
			return false
		end

		local config = SionConfig:GetCurrentModeConfig()
		local tracking = SionConfig.tracking

		-- 获取最佳目标
		local best_target = nil
		local best_score = 0
		local target_info = {}

		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and AsUtility.isTarget(enemy) and enemy.pos:dist(player.pos) <= config.max_range then
				-- 预测敌人位置
				local predicted_pos = AsUtility.GetPredictedPos2(enemy, tracking.prediction_time)
				local predicted_vec3 = vec3(predicted_pos.x, enemy.y, predicted_pos.y)

				-- 计算角度
				local direction_to_target = (predicted_vec3 - player.pos):norm()
				local current_direction = player.direction
				local angle_diff = math.acos(math.max(-1, math.min(1, current_direction:dot(direction_to_target))))
				local angle_deg = math.deg(angle_diff)

				-- 计算距离
				local distance = enemy.pos:dist(player.pos)
				local health_percent = enemy.health / enemy.maxHealth

				-- 使用配置计算分数
				local total_score = SionConfig:CalculateTargetScore(
					distance,
					angle_deg,
					health_percent,
					enemy.charName
				)

				-- 存储目标信息用于调试
				if SionConfig.debug.enabled then
					table.insert(target_info, {
						name = enemy.charName,
						distance = distance,
						angle = angle_deg,
						health_percent = health_percent,
						score = total_score
					})
				end

				if total_score > best_score and total_score > config.min_score then
					best_target = enemy
					best_score = total_score
				end
			end
		end

		-- 调试信息
		if SionConfig.debug.enabled and SionConfig.debug.log_tracking_decisions then
			if best_target then
				sadebug(string.format("Smart R Tracking: %s (Score: %.2f)",
					best_target.charName, best_score))
			else
				sadebug("Smart R Tracking: No suitable target found")
			end

			if SionConfig.debug.show_score_calculation then
				for _, info in ipairs(target_info) do
					sadebug(string.format("%s: D=%.0f A=%.1f H=%.1f S=%.2f",
						info.name, info.distance, info.angle, info.health_percent*100, info.score))
				end
			end
		end

		-- 如果找到了好目标，追踪它
		if best_target then
			player:attack(best_target)
			return true
		end

		return false
	end

	-- 同步菜单设置到配置
	local function SyncMenuToConfig()
		if not Sion.menu or not Sion.menu.r then return end

		-- 同步智能追踪设置
		SionConfig.smart_tracking.enabled = Sion.menu.r.smart_tracking:get()

		-- 同步追踪模式
		local mode_index = Sion.menu.r.tracking_mode:get()
		local modes = {"aggressive", "conservative", "balanced"}
		if modes[mode_index] then
			SionConfig:SetTrackingMode(modes[mode_index])
		end

		-- 同步范围设置
		local config = SionConfig:GetCurrentModeConfig()
		config.max_range = Sion.menu.r.smart_range:get()
		config.min_score = Sion.menu.r.min_score:get() / 100.0

		-- 同步优先级设置
		if Sion.menu.r.prioritize_low_hp:get() then
			config.prefer_low_health = true
			config.health_bonus = 1.3
		else
			config.prefer_low_health = false
			config.health_bonus = 1.0
		end
	end

	Sion.DirectionIsWall = function()
		for i=50, 700, 50 do
			local end_pos = player.pos + player.direction * i
			if navmesh.isWall(end_pos) then
				return true
			end
		end
		return false
	end
	
	
	function Sion:get_pos(pos)
		if navmesh.isWall(pos) then
			local drop_pos, is_grass = navmesh.wall_drop_pos(pos)
			if drop_pos then
				pos = vec3(drop_pos.x,0,drop_pos.y)
			else
				return vec3(pos.x,0,pos.y)
			end
			return pos
		end
		return vec3(pos.x,0,pos.y)
	end
	local shot_time = 0
	local logicR = function()
		--preds.linear.get_prediction(input, tar, src)
		--preds.circular.get_prediction(input, tar, src)
		--pred.collision.get_prediction(input, pred_result, ign_obj)
		
		if Sion.menu.r.mode:get() == 3 then
			if player.buff['sionr'] and Sion.menu.r.to:get() then
				if Sion.menu.r.zt:get() then
					return
				end
				if Sion.menu.r.stop:get() then
					local pos = player.path.serverPos2D
					--player:attackmove(pos)
					player:attackmove(Sion:get_pos(pos))
					player:attack(player)
					player:attack(player)
					player:attackmove(Sion:get_pos(mousePos2D))
					return
				end
				if Sion.menu.r.to_target:get() or AsTargetSelectorMenu.focus then
					if AsTargetSelectorMenu.focus then
						player:attack(AsTargetSelectorMenu.focus)
						sadebug("t2")
						return
					end
					local target = AsTargetSelectorMenu.gettarget(Sion.menu.r.to_target_range:get(),1,nil,7)
					if target then
						player:attack(target)
						sadebug("t1")
						return
					end
				end
				local preds_pos = player.pos
				
				local can  = true
				objManager.loop(function(obj)
					if obj and obj.isTargetable and obj.isVisible and obj.team ~= TEAM_ALLY and not obj.isDead then
						if not not_name[obj.name] then
							local preds_pos_obj = obj.pos
							local range = 800
							if obj.type == 2 and not obj.path.isActive then
								range = 400
							end
							if preds_pos_obj:dist(preds_pos) <= range then
								can = false
							end
						end
					end
				end)
				
				if can then
					player:attackmove(mousePos)
					
				else
					local rtarget = GetRTarget()
					if rtarget then
						player:attack(rtarget)
					end
				end
			end
		end
		
		if Sion.menu.r.mode:get() == 1 then
			-- 智能追踪模式
			if player.buff['sionr'] and Sion.menu.r.to:get() then
				if Sion.menu.r.zt:get() then
					return
				end

				if Sion.menu.r.stop:get() then
					local pos = player.path.serverPos2D
					player:attackmove(Sion:get_pos(pos))
					player:attack(player)
					player:attack(player)
					player:attackmove(Sion:get_pos(mousePos2D))
					sadebug("fleemove")
					return
				end

				-- 优先使用智能追踪
				if SmartRTracking() then
					return
				end

				-- 如果智能追踪没有找到目标，使用原有逻辑
				if Sion.menu.r.to_target:get() or AsTargetSelectorMenu.focus then
					if AsTargetSelectorMenu.focus then
						player:attack(AsTargetSelectorMenu.focus)
						sadebug("focus_target")
						return
					end
					local target = AsTargetSelectorMenu.gettarget(Sion.menu.r.to_target_range:get(),1,nil,7)
					if target then
						player:attack(target)
						sadebug("ts_target")
						return
					end
				end

				-- 检查周围是否有敌人
				local count1 = AsUtility.CountEnemiesNear(player.path.serverPos,850)
				local count2 = #AsUtility.count_xb_in_range(player.path.serverPos,850)
				local count3 = #AsUtility.count_yg_in_range(player.path.serverPos,850)
				local count = count1 + count2 + count3

				if count > 0 then
					local rtarget = GetRTarget()
					if rtarget then
						player:attack(rtarget)
						Sion.last_rtarget = rtarget
						sadebug("backup_target")
						return
					else
						if Sion.last_rtarget then
							player:attack(Sion.last_rtarget)
							sadebug("last_target")
							return
						else
							player:attackmove(Sion:get_pos(mousePos2D))
							sadebug("move_to_mouse")
							return
						end
					end
				else
					local end_pos = player.pos2D + ( mousePos2D - player.pos2D):norm() * player.moveSpeed
					player:attackmove(Sion:get_pos(end_pos))
					sadebug("move_forward")
				end
			end

			-- 逃跑模式的智能追踪
			if player.buff['sionr'] and Sion.menu.keys.fleekey:get() then
				local count1 = AsUtility.CountEnemiesNear(player.path.serverPos,850)
				local count2 = #AsUtility.count_xb_in_range(player.path.serverPos,850)
				local count3 = #AsUtility.count_yg_in_range(player.path.serverPos,850)
				local count = count1 + count2 + count3

				if count > 0 then
					-- 在逃跑时也使用智能追踪
					if SmartRTracking() then
						return
					end

					local rtarget = GetRTarget()
					if rtarget then
						player:attack(rtarget)
						Sion.last_rtarget = rtarget
						sadebug("flee_target")
						return
					else
						if Sion.last_rtarget then
							player:attack(Sion.last_rtarget)
							sadebug("flee_last_target")
							return
						else
							player:attackmove(Sion:get_pos(mousePos2D))
							sadebug("flee_mouse")
							return
						end
					end
				else
					local end_pos = player.pos2D + ( mousePos2D - player.pos2D):norm() * player.moveSpeed
					player:attackmove(Sion:get_pos(end_pos))
					sadebug("flee_forward")
				end
				return
			end
		end
		
		
		
		--旧版本
		
		if Sion.menu.r.mode:get() == 2 then
			if player.buff['sionr'] and Sion.menu.r.stop:get() then
				local pos = player.path.serverPos
				player:attackmove(pos)
				player:attack(player)
				return
			end
			
			if Sion.menu.r.target:get() then
				if Target and AsUtility.isTarget(Target) and player.buff['sionr'] then
					player:attack(Target)
					return true
				end
			end
			
			if player.buff['sionr'] and Sion.DirectionIsWall() and Sion.menu.r.fz:get() and not Sion.menu.r.zt:get() then
				local end_pos = player.pos2D + ( mousePos2D - player.pos2D):norm() * player.moveSpeed
				player:attackmove(Sion:get_pos(end_pos))
				orb.core.set_server_pause()
				return
			end
			
			if Sion.menu.r.to:get() and not Sion.menu.keys.fleekey:get() and not Sion.menu.r.zt:get() and orb.core.can_action() then
				
				if Sion.menu.r.close:get() then
					if AsUtility.UnderTurret(player.pos) then
						--orb.core.set_pause(0.5)
						return
					end
					
					local minionsInRange = AsUtility.GetMinionsInRange(800, TEAM_ENEMY)
					local monstersInRange = AsUtility.GetMinionsInRange(800, TEAM_NEUTRAL)
					if #minionsInRange+#monstersInRange > 0 then 
						--orb.core.set_pause(0.5)
						return
					end
					local end_pos = player.pos + player.direction * player.moveSpeed
					local minionsInRange = AsUtility.GetMinionsInRange(800, TEAM_ENEMY,end_pos)
					local monstersInRange = AsUtility.GetMinionsInRange(800, TEAM_NEUTRAL,end_pos)
					if #minionsInRange+#monstersInRange > 0 then 
						--orb.core.set_pause(1)
						return
					end
					
				end
				
				if Target and AsUtility.isTarget(Target) and player.buff['sionr'] and Sion.menu.r.target:get() then
					--local end_pos = player.pos2D + ( Target.pos2D - player.pos2D):norm() * player.moveSpeed
					--player:attackmove(Sion:get_pos(end_pos))
					--orb.core.set_pause(0.25)
					player:attack(Target)
					return true
				elseif player.buff['sionr'] then
					player:attackmove(Sion:get_pos(mousePos2D))
					return
				end
				
			end
			
		end
		
		
		
	end
	
	
	
	
	
	
	local resetaatime = 0
	local ResetAA=function()
	  if game.time<resetaatime+network.latency+player:basicAttack(0).windUpTime or orb.core.can_attack() then return end
		 orb.core.reset()
		 resetaatime=game.time
	end
	
	local AAspell={["ItemTiamatCleave"]=true,["ItemTitanicHydraCleave"]=true,}
	local useitem=function(unit)
	if not unit or not AsUtility.isTarget(unit) then return false end
	  for i = 6 , 11 do 
		local item = player:spellSlot(i)
		if item.isNotEmpty then
		if item.name and player:spellSlot(i).state==0  then
		if not orb.core.can_attack() and orb.core.can_action() and AAspell[item.name] then
		  player:castSpell('self', i)
		end
		if item.name=="YoumusBlade" then
			player:castSpell('self', i)
		elseif item.name=="ItemSwordOfFeastAndFamine" then
			player:castSpell('obj', i,unit)
		elseif item.name=="BilgewaterCutlass" then
			player:castSpell('obj', i,unit)
		end
		end
		end
	  end
	end
	
	
	

	
	local checkSpell = function(name)
		if name:find("shene") or  name:find("pykee") or  name:find("pyker") or  name:find("blindmonkqtwo") then
			return true
		end
		--  name:find("bandagetoss") or 
		if name:find("bandagetoss") or  name:find("sylase") or name:find("khazixe") or name:find("leonazenithblade") or name:find("gragase") or name:find("Sionanchordrag") or name:find("camilew") or name:find("renektondice") or name:find("renektonsliceanddice") or name:find("rakanw") or name:find("akalie") or name:find("threshqinternal")  then
			return true
		end
	
		return false
	end
	
	local SpellEvade = function() -- 打断突进
		
	end
	
	local SetMana = function()
		QMANA = Q.slot.level>0 and player.manaCost0 or 0
		WMANA = W.slot.level>0 and player.manaCost1 or 0
		EMANA = E.slot.level>0 and player.manaCost2 or 0
		RMANA = R.slot.level>0 and player.manaCost3 or 0
	end
	local Init = function()
		local evade_is = false
		local orb_is = false
		if player.isDead or player.buff["sionpassivezombie"] then
			if evade then
				evade.core.set_pause(math.huge)
				evade_is = true
			end
		else
			if evade and not evade_is and evade.core.is_paused() then
				evade.core.set_pause(0)
			end
		end
		
		if player.buff['sionq'] then 
			if evade then
				evade.core.set_pause(math.huge)
				evade_is = true
			end
			if not orb.core.is_paused() then
				orb.core.set_pause(math.huge)
				orb_is = true
			end
		else
			if evade and not evade_is and evade.core.is_paused() then
				evade.core.set_pause(0)
			end
			if SionCastDirection and not AsUtility.isq() then 
				SionCastDirection = nil
			end
			if orb.core.is_paused() and not orb_is then
				orb.core.set_pause(0)
			end
		end
		if player.buff['sionr'] then 
			if not orb.core.is_attack_paused() then
				orb.core.set_pause_attack(math.huge)
				orb_is = true
			end
			if evade  and evade.core.is_paused() then
				evade.core.set_pause(math.huge)
				evade_is = true
			end
		elseif not player.buff['sionq'] then
			if orb.core.is_attack_paused() and not orb_is then
				orb.core.set_pause_attack(0)
			end
			if evade and not evade_is and evade.core.is_paused() then
				evade.core.set_pause(0)
			end
		end
	end
	
	local OkRhit = function(heros,hero)
		if #heros == 0 then return true end
		for i = 1, #heros do
			local t = heros[i]
			if t == hero then return false end
		end
	
		return true
	end
	
	local GetEMinis = function()
		local in_attack_range = false
		local rt = false
		
		local valid = {}
		local validaa = {}
		local minions = objManager.minions
		for i = 0, minions.size[TEAM_ENEMY] - 1 do
			local minion = minions[TEAM_ENEMY][i]
			if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (E.range * E.range) and minion.maxHealth > 10  then
				local dist = player.pos2D:dist(minion.pos2D)
				table.insert(valid, minion)
				if dist < 800 then
				   table.insert(validaa, minion)
				   in_attack_range = true
				end
			end
		end
		if	#validaa == 0 then
			for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
				local minion = minions[TEAM_NEUTRAL][i]
				if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (E.range * E.range) and minion.maxHealth > 10  then
					local dist = player.pos2D:dist(minion.pos2D)
					table.insert(valid, minion)
					if dist < 800 then
					   table.insert(validaa, minion)
					   in_attack_range = true
					end
					rt = true
				end
			end
		end
		local cast_obj, max_count = nil, 0
		for i, minion_a in ipairs(validaa) do
			local distance = math.min(player.attackRange, math.max(365, minion_a.pos2D:dist(player.pos2D)))
			local direction = ( minion_a.pos - player.pos):norm()
			local hit_count = 1
			local heros = {}
			for k, minion_b in ipairs(valid) do
				if k~=i then
					for j=50, E.range, 50 do
						local extendedPos = player.pos + direction * j
						if minion_b.pos:dist(extendedPos) < 50 then
							if OkRhit(heros,minion_b) then
								hit_count=hit_count+1
								table.insert(heros, minion_b)
							end
							
						end
					end
				end
			end
			if hit_count>= max_count then
				max_count = hit_count
				cast_obj = minion_a
			end
		end
		return cast_obj,max_count,in_attack_range,rt
	end
	local function point_on_line_seg(line_start, line_end, point)
	  local ratio = ((point.x - line_start.x)*(line_end.x - line_start.x) + (point.y - line_start.y)*(line_end.y - line_start.y))/((line_end.x - line_start.x)^2 + (line_end.y - line_start.y)^2)
	  if ratio>=0 and ratio<=1 then
		return vec2(line_start.x + ratio*(line_end.x - line_start.x), line_start.y + ratio*(line_end.y - line_start.y))
	  end
	  return nil
	end
	
	local GetQMinis = function()
		local minions = objManager.minions
		local in_attack_range = false
		local valid = {}
		for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
			local minion = minions[TEAM_NEUTRAL][i]
			if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (Q.range * Q.range) and  not AsUtility.WardName(minion)   then
				local tpred = minion.path.active and preds.core.project(vec2(player.path.serverPos.x, player.path.serverPos.z), minion.path, 0.25, 1400, minion.moveSpeed) or vec2(minion.path.serverPos.x, minion.path.serverPos.z)
					
				local dist = player.pos2D:dist(tpred)
				if dist < Q.range then
				  table.insert(valid, minion)
				end
				if dist < player.attackRange + 65 then
				  in_attack_range = true
				end
			end
		end
		local cast_pos, max_count = nil, 0
		for i, minion_a in ipairs(valid) do
			local distance = math.min(Q.range, math.max(365, minion_a.pos2D:dist(player.pos2D)+50))
			
			local current_pos, hit_count = player.pos2D + (minion_a.pos2D-player.pos2D):norm()*distance, 1
			for k, minion_b in ipairs(valid) do
				if i~=k then
					local tpred = minion_b.path.active and preds.core.project(vec2(player.path.serverPos.x, player.path.serverPos.z), minion_b.path, 0.25, 1400, minion_b.moveSpeed) or vec2(minion_b.path.serverPos.x, minion_b.path.serverPos.z)
					
					local closest_point = point_on_line_seg(player.pos2D, current_pos, tpred)
					if closest_point and closest_point:dist(tpred) < 149+minion_b.boundingRadius then
						hit_count=hit_count+1
					end
				end
			end
			if not cast_pos or hit_count>max_count then
				cast_pos, max_count = current_pos, hit_count
			end
		end
		if cast_pos then
			return cast_pos,max_count,in_attack_range,true
		end
		
		if #valid == 0 then
			for i = 0, minions.size[TEAM_ENEMY] - 1 do
				local minion = minions[TEAM_ENEMY][i]
				if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (Q.range * Q.range) and   not AsUtility.WardName(minion)  then
					
					local tpred = minion.path.active and preds.core.project(vec2(player.path.serverPos.x, player.path.serverPos.z), minion.path, 0.25, 1400, minion.moveSpeed) or vec2(minion.path.serverPos.x, minion.path.serverPos.z)
					local dist = player.pos2D:dist(tpred)
					if dist < Q.range then
					  table.insert(valid, minion)
					end
					if dist < player.attackRange + 65 then
					  in_attack_range = true
					end
				end
			end
			cast_pos, max_count = nil, 0
			for i, minion_a in ipairs(valid) do
				local distance = math.min(Q.range, math.max(365, minion_a.pos2D:dist(player.pos2D)+50))
				local current_pos, hit_count = player.pos2D + (minion_a.pos2D-player.pos2D):norm()*distance, 1
				for k, minion_b in ipairs(valid) do
					if i~=k then
						local tpred = minion_b.path.active and preds.core.project(vec2(player.path.serverPos.x, player.path.serverPos.z), minion_b.path, 0.25, 1400, minion_b.moveSpeed) or vec2(minion_b.path.serverPos.x, minion_b.path.serverPos.z)
					
						local closest_point = point_on_line_seg(player.pos2D, current_pos,tpred)
						if closest_point and closest_point:dist(tpred) < 149+minion_b.boundingRadius then
							hit_count=hit_count+1
						end
					end
				end
				if not cast_pos or hit_count>max_count then
					cast_pos, max_count = current_pos, hit_count
				end
			end
		
		end
		return cast_pos,max_count,in_attack_range,false	
	end
	
	
	
	local Farm = function()
		if Sion.menu.farm.qx:get() then
			if Sion.menu.farm.qxjs:get() and #AsUtility.count_enemies_in_range(player.pos, 1600)>0 and not iswxhl then 
				return 
			end
			if AsUtility.isq()  and Sion.menu.farm.quse:get() and Sion.menu.farm.qmana:get() <= AsUtility.GetPercentMana() and game.time > lastAA  then 
				local cast_pos,max_count,in_attack_range,rt = GetQMinis()
				if not player.buff['sionq'] then
					if not rt and cast_pos and (not in_attack_range or not orb.core.can_attack()) and max_count >= Sion.menu.farm.qqxsl:get() then
						player:castSpell('pos', 0, vec3(cast_pos.x, game.mousePos.y, cast_pos.y))
						orb.core.set_server_pause()
						return
					end
					if rt and cast_pos and game.time > lastAA and (not in_attack_range or not orb.core.can_attack())  then
						player:castSpell('pos', 0, vec3(cast_pos.x, game.mousePos.y, cast_pos.y))
						orb.core.set_server_pause()
						return
					end
				else
					if max_count == 0 then
						player:castSpell("release",0,player.pos)
						orb.core.set_server_pause()
						return
					end
				end
				
			end
			
		---- ~~ 使用E ~~  --
			if AsUtility.ise() and Sion.menu.farm.euse:get() and Sion.menu.farm.emana:get() < AsUtility.GetPercentMana() and game.time > lastAA  then 
				local cast_obj,max_count,in_attack_range,rt = GetEMinis()
				if cast_obj and max_count and (not in_attack_range or not orb.core.can_attack()) and not rt and (max_count>=Sion.menu.farm.eqxsl:get() or iswxhl) then
					player:castSpell("pos",2,cast_obj.pos)
					return
				end
				if cast_obj and max_count and (not in_attack_range or not orb.core.can_attack()) and  rt then
					player:castSpell("pos",2,cast_obj.pos)
					return
				end
			end
		end
	end
	
	local Flee = function()
		
		if not player.buff['sionr'] then
			player:move(mousePos)
		elseif player.buff['sionr'] and Sion.menu.r.mode:get() == 2 and not Sion.menu.r.zt:get()  then
			if player.buff['sionr'] and Sion.DirectionIsWall() and Sion.menu.r.fz:get() then
				player:attackmove(mousePos)
				return
			end
			if Sion.menu.r.close:get() then
				local minionsInRange = AsUtility.GetMinionsInRange(700, TEAM_ENEMY)
				local monstersInRange = AsUtility.GetMinionsInRange(700, TEAM_NEUTRAL)
				if #minionsInRange > 0 or #monstersInRange > 0 then return end
			end
			player:attackmove(mousePos)
			return
		end
		
	
	end
	
	Sion.on_tick = function()
		
		
		if player.buff["sionpassivezombie"] then return end
		SetMana()
		Init()
		Target = GetTarget()
		logicR()
		if AsUtility.ise() then
			logicE()
		end
		if AsUtility.isw() then
			logicW()
		end
		if AsUtility.isq() then
			logicQ()
		end
		
	
		if Sion.menu.keys.clearkey:get()  then
			Farm()
		end
		
		if Sion.menu.keys.fleekey:get()  then
			Flee()
		end
		
		--[[
		if GetQrange() >= 675 then
			--sadebug(1)
		end--]]
		
	end
	local AutoTargetEvade = function(spell)
		
	end	
	local AutoGap = function(spell)
		
	end
	
	local GapTime = 0
	local AutoInterrupt = function(spell)
		
	end
	
	local Onspell = function(spell)
		
		
		if spell and spell.name and spell.owner and spell.owner == player and (spell.name:lower():find("attack") or spell.isBasicAttack) then
			lastAA = game.time + spell.windUpTime+network.latency
			lasttarget = spell.target
			lasttime = game.time
		end
		
		if spell and spell.name and spell.owner and spell.owner == player  and spell.name:find("SionQ") then
			local direction = (spell.endPos - player.path.serverPos):norm()
			SionCastDirection = direction
			lastq = game.time + 0.25
		end
		if spell and player:spellSlot(0).name == "SionQ" and spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY and spell.target == player and Sion.menu.w.auto:get() and W.slot.name == "SionW" then
			if not spell.name:find("crit") then
				if not spell.name:find("BasicAttack") and not player.buff['sionq'] then
					player:castSpell("self", 1)
					sadebug("Auto W " .. spell.name)
					return true
				end
			end
		end
		if spell and spell.type == TYPE_HERO and spell.team ~= TEAM_ALLY and spell.target and spell.target == player then
			print(spell.name)
			if player.buff['sionq'] and Sion.menu.keys.combokey:get() and Sion.menu.q.combo:get()  then
				--player:castSpell("release", 0,player.pos)
			
			end
		end
		if Sion.menu.w.aa:get() and player:spellSlot(0).name == "SionQ" and not player.buff['sionq'] and W.slot.name == "SionW" then
			if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY and spell.target == player  then
				if spell.name:find("BasicAttack") or spell.name:find("crit") then
					if (player.health / player.maxHealth) * 100 <= Sion.menu.w.aahp:get() then
						player:castSpell("self", 1)
						sadebug("Auto W BasicAttack")
						return true
					end
				end
			end
			if spell.owner.type == TYPE_TURRET and spell.owner.team == TEAM_ENEMY and spell.target == player and not player.buff['sionq'] and W.slot.name == "SionW" then
				if (player.health / player.maxHealth) * 100 <= Sion.menu.w.aahp:get() then
					player:castSpell("self", 1)
					return true
				end	
			end
			
		end
		
	end

	
	local Ondraw = function()
		--[[
		local preds_pos = player.pos
				
		local can  = true
		objManager.loop(function(obj)
			if obj and obj.isTargetable and obj.isVisible and obj.team ~= TEAM_ALLY and not obj.isDead then
				if not not_name[obj.name] then
					local preds_pos_obj = obj.pos
					local range = 800
					if obj.type == 2 and not obj.path.isActive then
						range = 400
					end
					if preds_pos_obj:dist(preds_pos) <= range then
						can = false
					end
				end
			end
		end)
		
		if can then
		
			
		else
			local rtarget = GetRTarget()
			if rtarget then
				local v1 = rtarget.pos
				local radius = 1000
				local line_width = 1
				local color = 0xFFFFFFFF
				local points_n = 16
				minimap.draw_circle(v1, radius, line_width, color, points_n)
				graphics.draw_circle(v1, player.attackRange, 2, 0xFFFFFFFF, 32)
			end
		end--]]
		
		
		if player.isOnScreen and not player.isDead then
			local pos = graphics.world_to_screen(vec3(player.x, player.y, player.z))
			
			--[[
			if player.buff['sionq'] then
				local end_pos = player.pos + player.direction * 800
				local tpred = AsUtility.GetPredictedPos2(Target,0.25)
				local line = mathf.dist_line_vector(tpred,vec2(end_pos.x,end_pos.z),player.pos2D)
				if line < Sion.menu.q.line:get() and vec3(tpred.x, Target.y,tpred.y):dist(player.pos) < 850 and end_pos:dist(player.pos) >  end_pos:dist(vec3(tpred.x,Target.y,tpred.y)) then -- 倆边
					graphics.draw_text_2D('false', 44, game.cursorPos.x, game.cursorPos.y, 0xFFFFFFFF)
				else
					local line1 = mathf.dist_line_vector(Target.pos2D,vec2(end_pos.x,end_pos.z),player.pos2D)
					local pointSegment, pointLine, isOnSegment = AsUtility.VectorPointProjectionOnLineSegment3(player.pos,end_pos,Target)
					if not isOnSegment then --不在Q范围内
						
					elseif line > Sion.menu.q.line:get() then --不在Q范围内
					
					end
					graphics.draw_text_2D(tostring(isOnSegment), 44, game.cursorPos.x, game.cursorPos.y, 0xFFFFFFFF)
					graphics.draw_text_2D(tostring(line1), 44, game.cursorPos.x + 88, game.cursorPos.y + 88, 0xFFFFFFFF)
				end
			end
			--]]
			--[[
			if SionCastDirection then
				local tpred = AsUtility.GetPredictedPos2(Target,0.25)
				local end_pos = player.pos + SionCastDirection * 850
				local line = mathf.dist_line_vector(tpred,vec2(end_pos.x,end_pos.z),player.pos2D)
				if line < Sion.menu.q.line:get() and vec3(tpred.x,rt_target = enemies.y,tpred.y):dist(player.pos) < 850 and end_pos:dist(player.pos) >  end_pos:dist(vec3(tpred.x,enemies.y,tpred.y)) then -- 倆边
					graphics.draw_text_2D('false', 44, game.cursorPos.x, game.cursorPos.y, 0xFFFFFFFF)

				else
					rt = true;
					local line1 = mathf.dist_line_vector(rt_target = enemies.pos2D,vec2(end_pos.x,end_pos.z),player.pos2D)
					
					if line1 > Sion.menu.q.line:get()  then
						local tpred2 = AsUtility.GetPredictedPos2(rt_target = enemies,1.1)
						if vec3(tpred2.x,rt_target = enemies.y,tpred2.y):dist(player.pos) < 840 then
							
						end
					end
				end
			end--]]
			--[[
			if player.buff['sionr'] then
				--local end_pos = player.pos + player.direction * player.moveSpeed
				local end_pos = player.pos + ( mousePos - player.pos):norm() * player.moveSpeed
				graphics.draw_circle(end_pos, 30, 1, 0xFF3B92EF, 100)
			end
			
			local rtarget = GetRTarget()
			if rtarget then
			
				graphics.draw_circle(rtarget.pos, 100, 1, 0xFFF40000, 100)
				minimap.draw_circle(rtarget.pos, 200, 2, 0xFFF40000, 32)
			end
				local end_pos = player.pos + SionCastDirection * 675
				graphics.draw_circle(end_pos, 30, 1, 0xFF3B92EF, 100)
				
				local a = mathf.angle_between(vec2(end_pos.x,end_pos.z),mousePos2D,player.pos2D)
				

				local minionAngle = math.abs(math.deg(a))
				graphics.draw_text_2D(tostring(mathf.dist_line_vector(mousePos2D,vec2(end_pos.x,end_pos.z),player.pos2D)), 18, pos.x - 40, pos.y + 20, graphics.argb(254, 0, 204, 255))
				--graphics.draw_text_2D(tostring(mathf.dist_line_vector(player.pos2D,vec2(end_pos.x,end_pos.z),mousePos2D)), 18, pos.x - 40, pos.y + 20, graphics.argb(254, 0, 204, 255))
				--graphics.draw_text_2D(tostring(mathf.dist_line_vector(vec2(end_pos.x,end_pos.z),player.pos2D,mousePos2D)), 18, pos.x - 40, pos.y + 20, graphics.argb(254, 0, 204, 255))
				--graphics.draw_text_2D(tostring(minionAngle), 18, pos.x - 40, pos.y + 20, graphics.argb(254, 0, 204, 255))
				print(minionAngle)
			--]]
			if Sion.menu.r.to_target:get() and not Sion.menu.keys.fleekey:get() then
				if chinese and  Sion.menu.r.to:get()  then
					local text_msg = "追踪目标"
					local target = AsTargetSelectorMenu.focus or AsTargetSelectorMenu.gettarget(Sion.menu.r.to_target_range:get(),1,nil,7)
					if target then
						text_msg = text_msg .. target.displayNameCn
					end
					graphics.draw_text_2D(text_msg , 20, graphics.width/2.5, graphics.height/1.3, graphics.argb(255, 128,128,0	 ))
				elseif Sion.menu.r.to:get()  then
					local text_msg = "Track target"
					local target = AsTargetSelectorMenu.focus or AsTargetSelectorMenu.gettarget(3000,1,nil,7)
					if target then
						text_msg = text_msg .. target.displayName
					end
					graphics.draw_text_2D(text_msg, 20, graphics.width/2.5, graphics.height/1.3, graphics.argb(255, 128,128,0	 ))
				end
			end
			
			if Sion.menu.farm.qx:get() then
				if chinese then
					graphics.draw_text_2D("技能清线   : 开", 14, pos.x - 40, pos.y + 40, graphics.argb(254, 0, 204, 255))
				else
					graphics.draw_text_2D("Farm   : on", 14, pos.x - 40, pos.y + 40, graphics.argb(254, 0, 204, 255))
				end
			else
				if chinese then
					graphics.draw_text_2D("技能清线   : 关", 14, pos.x - 40, pos.y + 40, graphics.argb(255, 218, 34, 34))
				else
					graphics.draw_text_2D("Farm   : off", 14, pos.x - 40, pos.y + 40, graphics.argb(254, 0, 204, 255))
				end
			end
			if Sion.menu.e.auto:get() then
				if chinese then
					graphics.draw_text_2D("自动E  : 开", 14, pos.x - 40, pos.y + 60, graphics.argb(254, 0, 204, 255))
					--graphics.draw_text_2D(tostring(SionCastDirection), 18, pos.x - 40, pos.y + 80, graphics.argb(255, 218, 34, 34))
				else
					graphics.draw_text_2D("AutoE  : on", 14, pos.x - 40, pos.y + 60, graphics.argb(254, 0, 204, 255))
				end
			else
				if chinese then
					graphics.draw_text_2D("自动E  : 关", 14, pos.x - 40, pos.y + 60, graphics.argb(255, 218, 34, 34))
					
				else
					graphics.draw_text_2D("AutoE  : off", 14, pos.x - 40, pos.y + 60, graphics.argb(254, 0, 204, 255))
				end
			end
			if chinese then
				if Sion.menu.r.to:get() then
					--graphics.draw_text_2D("追踪: 开", 16,  graphics.width/1.38, graphics.height-30, 0xFF41BD3A)
				else
					--graphics.draw_text_2D("追踪: 关", 16,  graphics.width/1.38, graphics.height-30, 0xFFF40000)
				end
			else
				if Sion.menu.r.to:get() then
					--graphics.draw_text_2D("Track: ON", 16,  graphics.width/1.38, graphics.height-30, 0xFF41BD3A)
				else
					--graphics.draw_text_2D("Track: OFF", 16,  graphics.width/1.38, graphics.height-30, 0xFFF40000)
				end
			end
			
			
		end
	end

	

	local function after_attack()
		
		orb.combat.set_invoke_after_attack(false)
	end

	
	local function on_issue_order(order, pos, obj)
		
		if order==3 then
		   lastAA = game.time + player:basicAttack(0).windUpTime+network.latency
		  
		end
	end
	
	local function on_cast_spell(slot, startpos, endpos, nid)
		
		if slot == 0 and player:spellSlot(0).name == "SionQ"  then
			orb.core.set_pause_attack(math.huge)
			lastq = game.time + 0.25
		end
		if slot == 3 and player:spellSlot(0).name == "SionQ" then
			orb.core.set_pause_attack(math.huge)
		end
	end
	local function on_delete_minion(obj)
		if Sion.last_rtarget and obj and obj.ptr == Sion.last_rtarget.ptr then
			Sion.last_rtarget = nil
		end
	
	end
	
	cb.add(cb.spell, Onspell)
	cb.add(cb.castspell, on_cast_spell)
	cb.add(cb.issueorder, on_issue_order)
	cb.add(cb.delete_minion, on_delete_minion)
	if player.charName ~= "Viego" then
		cb.add(cb.draw, Ondraw)
		orb.combat.register_f_pre_tick(Sion.on_tick)
	end
end

return Sion